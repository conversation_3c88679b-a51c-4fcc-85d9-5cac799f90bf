import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:provider/provider.dart';

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../models/song.dart';
import '../../models/playlist.dart';
import '../../services/auth_service.dart';

class CreatePlaylistScreen extends StatefulWidget {
  final Song? initialSong;

  const CreatePlaylistScreen({
    super.key,
    this.initialSong,
  });

  @override
  State<CreatePlaylistScreen> createState() => _CreatePlaylistScreenState();
}

class _CreatePlaylistScreenState extends State<CreatePlaylistScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  bool _isPublic = false;
  bool _isCreating = false;
  String _selectedMood = '';
  String _selectedGenre = '';
  
  final List<String> _moods = [
    'Happy', 'Sad', 'Energetic', 'Chill', 'Romantic', 'Angry', 'Peaceful', 'Nostalgic'
  ];
  
  final List<String> _genres = [
    'Pop', 'Rock', 'Hip Hop', 'Jazz', 'Classical', 'Electronic', 'Country', 'R&B'
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.spotifyBlack,
        title: const Text(
          'Create Playlist',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppTheme.spotifyWhite),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          TextButton(
            onPressed: _isCreating ? null : _createPlaylist,
            child: Text(
              'Create',
              style: TextStyle(
                color: _isCreating ? AppTheme.spotifyOffWhite : AppTheme.spotifyGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Playlist cover placeholder
              _buildPlaylistCover(),
              
              const SizedBox(height: 24),
              
              // Playlist name
              _buildNameField(),
              
              const SizedBox(height: 16),
              
              // Description
              _buildDescriptionField(),
              
              const SizedBox(height: 24),
              
              // Public/Private toggle
              _buildPrivacyToggle(),
              
              const SizedBox(height: 24),
              
              // Mood selection
              _buildMoodSelection(),
              
              const SizedBox(height: 24),
              
              // Genre selection
              _buildGenreSelection(),
              
              const SizedBox(height: 24),
              
              // AI generation option
              _buildAIGenerationOption(),
              
              if (widget.initialSong != null) ...[
                const SizedBox(height: 24),
                _buildInitialSongInfo(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaylistCover() {
    return Center(
      child: GestureDetector(
        onTap: () {
          // TODO: Implement image picker
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Image picker coming soon!'),
              backgroundColor: AppTheme.spotifyGreen,
            ),
          );
        },
        child: Container(
          width: 160,
          height: 160,
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppTheme.spotifyLightGrey),
          ),
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_photo_alternate,
                size: 48,
                color: AppTheme.spotifyOffWhite,
              ),
              SizedBox(height: 8),
              Text(
                'Add Cover',
                style: TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      style: const TextStyle(color: AppTheme.spotifyWhite),
      decoration: InputDecoration(
        labelText: 'Playlist Name',
        labelStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.spotifyGreen, width: 2),
        ),
        filled: true,
        fillColor: AppTheme.spotifyGrey,
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a playlist name';
        }
        if (value.trim().length < AppConstants.minPlaylistNameLength) {
          return 'Name must be at least ${AppConstants.minPlaylistNameLength} characters';
        }
        if (value.trim().length > AppConstants.maxPlaylistNameLength) {
          return 'Name must be less than ${AppConstants.maxPlaylistNameLength} characters';
        }
        return null;
      },
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      style: const TextStyle(color: AppTheme.spotifyWhite),
      maxLines: 3,
      decoration: InputDecoration(
        labelText: 'Description (Optional)',
        labelStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.spotifyGreen, width: 2),
        ),
        filled: true,
        fillColor: AppTheme.spotifyGrey,
      ),
    );
  }

  Widget _buildPrivacyToggle() {
    return Row(
      children: [
        Switch(
          value: _isPublic,
          onChanged: (value) {
            setState(() {
              _isPublic = value;
            });
          },
          activeColor: AppTheme.spotifyGreen,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _isPublic ? 'Public Playlist' : 'Private Playlist',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                _isPublic 
                    ? 'Anyone can see and follow this playlist'
                    : 'Only you can see this playlist',
                style: const TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMoodSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Mood (Optional)',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _moods.map((mood) {
            final isSelected = _selectedMood == mood;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedMood = isSelected ? '' : mood;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyGrey,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyLightGrey,
                  ),
                ),
                child: Text(
                  mood,
                  style: TextStyle(
                    color: isSelected ? AppTheme.spotifyBlack : AppTheme.spotifyWhite,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildGenreSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Genre (Optional)',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _genres.map((genre) {
            final isSelected = _selectedGenre == genre;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedGenre = isSelected ? '' : genre;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyGrey,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyLightGrey,
                  ),
                ),
                child: Text(
                  genre,
                  style: TextStyle(
                    color: isSelected ? AppTheme.spotifyBlack : AppTheme.spotifyWhite,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildAIGenerationOption() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.spotifyGreen.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome,
                color: AppTheme.spotifyGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'AI-Powered Playlist',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Let AI generate songs based on your mood and genre selection',
            style: TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                // TODO: Implement AI generation
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('AI generation coming soon!'),
                    backgroundColor: AppTheme.spotifyGreen,
                  ),
                );
              },
              icon: const Icon(Icons.auto_awesome, size: 16),
              label: const Text('Generate with AI'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.spotifyGreen,
                side: const BorderSide(color: AppTheme.spotifyGreen),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialSongInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Starting with:',
            style: TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: AppTheme.spotifyLightGrey,
                ),
                child: const Icon(
                  Icons.music_note,
                  color: AppTheme.spotifyOffWhite,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.initialSong!.title,
                      style: const TextStyle(
                        color: AppTheme.spotifyWhite,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      widget.initialSong!.displayArtist,
                      style: const TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _createPlaylist() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isCreating = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final currentUser = authService.currentUser;
      
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      // Create playlist
      final playlist = Playlist(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: currentUser.id,
        isPublic: _isPublic,
        songs: widget.initialSong != null ? [widget.initialSong!] : [],
        tags: [
          if (_selectedMood.isNotEmpty) _selectedMood,
          if (_selectedGenre.isNotEmpty) _selectedGenre,
        ],
      );

      // Save to Hive
      final playlistBox = Hive.box<Playlist>(AppConstants.playlistBoxKey);
      await playlistBox.put(playlist.id, playlist);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playlist "${playlist.name}" created!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create playlist: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }
}
