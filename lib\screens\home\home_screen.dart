import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';
import '../../widgets/playlist_card.dart';
import '../../widgets/quick_access_tile.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 17) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            backgroundColor: AppTheme.spotifyBlack,
            elevation: 0,
            floating: true,
            snap: true,
            title: Text(_getGreeting(), style: AppTheme.sectionHeader),
            actions: [
              IconButton(
                icon: const Icon(Icons.notifications_outlined),
                onPressed: () {
                  // TODO: Navigate to notifications
                },
              ),
              IconButton(
                icon: const Icon(Icons.history),
                onPressed: () {
                  // TODO: Navigate to recently played
                },
              ),
              IconButton(
                icon: const Icon(Icons.settings_outlined),
                onPressed: () {
                  // TODO: Navigate to settings
                },
              ),
            ],
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quick Access Grid
                  _buildQuickAccessGrid(),

                  const SizedBox(height: 32),

                  // Recently Played Section
                  _buildRecentlyPlayedSection(),

                  const SizedBox(height: 32),

                  // Made For You Section
                  _buildMadeForYouSection(),

                  const SizedBox(height: 32),

                  // Popular Playlists Section
                  _buildPopularPlaylistsSection(),

                  const SizedBox(height: 100), // Bottom padding for mini player
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 3.2,
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      children: [
        QuickAccessTile(
          title: 'Liked Songs',
          imageUrl: null,
          icon: Icons.favorite,
          onTap: () {
            // TODO: Navigate to liked songs
          },
        ),
        QuickAccessTile(
          title: 'Recently Played',
          imageUrl: null,
          icon: Icons.history,
          onTap: () {
            // TODO: Navigate to recently played
          },
        ),
        QuickAccessTile(
          title: 'AI Discover',
          imageUrl: null,
          icon: Icons.auto_awesome,
          onTap: () {
            // TODO: Navigate to AI discover
          },
        ),
        QuickAccessTile(
          title: 'Voice Search',
          imageUrl: null,
          icon: Icons.mic,
          onTap: () {
            // TODO: Open voice search
          },
        ),
      ],
    );
  }

  Widget _buildRecentlyPlayedSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Recently played', style: AppTheme.sectionHeader),
            TextButton(
              onPressed: () {
                // TODO: Navigate to see all recently played
              },
              child: Text(
                'See all',
                style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.spotifyOffWhite,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Horizontal list of recently played items
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5, // Placeholder count
            itemBuilder: (context, index) {
              return Padding(
                padding: EdgeInsets.only(right: index < 4 ? 16 : 0),
                child: PlaylistCard(
                  title: 'Recently Played ${index + 1}',
                  subtitle: 'Your recent music',
                  imageUrl: null,
                  onTap: () {
                    // TODO: Handle tap
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMadeForYouSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Made for you', style: AppTheme.sectionHeader),

        const SizedBox(height: 16),

        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5,
            itemBuilder: (context, index) {
              final titles = [
                'Discover Weekly',
                'Release Radar',
                'Daily Mix 1',
                'Daily Mix 2',
                'AI Recommendations',
              ];

              final subtitles = [
                'Your weekly mixtape of fresh music',
                'Catch all the latest music from artists you follow',
                'Made for you',
                'Made for you',
                'Powered by AI',
              ];

              return Padding(
                padding: EdgeInsets.only(right: index < 4 ? 16 : 0),
                child: PlaylistCard(
                  title: titles[index],
                  subtitle: subtitles[index],
                  imageUrl: null,
                  onTap: () {
                    // TODO: Handle tap
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPopularPlaylistsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Popular playlists', style: AppTheme.sectionHeader),

        const SizedBox(height: 16),

        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5,
            itemBuilder: (context, index) {
              final titles = [
                'Today\'s Top Hits',
                'RapCaviar',
                'Pop Rising',
                'Rock Classics',
                'Chill Hits',
              ];

              final subtitles = [
                'The most played songs right now',
                'New music from hip-hop\'s biggest stars',
                'Pop music on the rise',
                'Rock legends & epic songs',
                'Kick back to the best new and recent chill hits',
              ];

              return Padding(
                padding: EdgeInsets.only(right: index < 4 ? 16 : 0),
                child: PlaylistCard(
                  title: titles[index],
                  subtitle: subtitles[index],
                  imageUrl: null,
                  onTap: () {
                    // TODO: Handle tap
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
