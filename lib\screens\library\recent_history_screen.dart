import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/theme/app_theme.dart';
import '../../models/song.dart';
import '../../services/auth_service.dart';
import '../../services/audio_player_service.dart';
import '../../widgets/song_tile.dart';
import '../playlist/add_to_playlist_screen.dart';

class RecentHistoryScreen extends StatefulWidget {
  const RecentHistoryScreen({super.key});

  @override
  State<RecentHistoryScreen> createState() => _RecentHistoryScreenState();
}

class _RecentHistoryScreenState extends State<RecentHistoryScreen> {
  List<Song> _recentSongs = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecentHistory();
  }

  Future<void> _loadRecentHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final currentUser = authService.currentUser;
      
      if (currentUser != null) {
        // In a real app, you'd fetch the actual Song objects from storage
        // For now, we'll create mock songs based on the user's recently played song IDs
        _recentSongs = currentUser.recentlyPlayed.asMap().entries.map((entry) {
          final index = entry.key;
          final songId = entry.value;
          return Song(
            id: songId,
            title: 'Recently Played Song ${index + 1}',
            artist: 'Artist ${index + 1}',
            album: 'Album ${index + 1}',
            duration: 180 + (index * 15),
            albumArt: null,
          );
        }).toList();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.spotifyBlack,
        title: const Text(
          'Recently Played',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.spotifyWhite),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: AppTheme.spotifyWhite),
            onPressed: _searchHistory,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: AppTheme.spotifyWhite),
            color: AppTheme.spotifyGrey,
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_all',
                child: ListTile(
                  leading: Icon(Icons.clear_all, color: AppTheme.errorRed),
                  title: Text('Clear All History', style: TextStyle(color: AppTheme.errorRed)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.file_download, color: AppTheme.spotifyWhite),
                  title: Text('Export History', style: TextStyle(color: AppTheme.spotifyWhite)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.spotifyGreen),
              ),
            )
          : _recentSongs.isEmpty
              ? _buildEmptyState()
              : _buildHistoryList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppTheme.spotifyGrey,
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.history,
              size: 60,
              color: AppTheme.spotifyOffWhite,
            ),
          ),
          
          const SizedBox(height: 24),
          
          Text(
            'No recent history',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Songs you play will appear here',
            style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.spotifyOffWhite,
            ),
          ),
          
          const SizedBox(height: 24),
          
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to search or home to find songs
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.spotifyGreen,
              foregroundColor: AppTheme.spotifyBlack,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
            child: const Text(
              'Discover Music',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList() {
    return Column(
      children: [
        // Header with stats
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.history,
                color: AppTheme.spotifyGreen,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                '${_recentSongs.length} recently played tracks',
                style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
                  color: AppTheme.spotifyWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        
        // Songs list
        Expanded(
          child: ListView.builder(
            itemCount: _recentSongs.length,
            itemBuilder: (context, index) {
              final song = _recentSongs[index];
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
                child: SongTile(
                  song: song,
                  onTap: () => _playSong(index),
                  subtitle: _buildSubtitle(index),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Time played (mock)
                      Text(
                        _getTimeAgo(index),
                        style: const TextStyle(
                          color: AppTheme.spotifyOffWhite,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 8),
                      PopupMenuButton<String>(
                        icon: const Icon(Icons.more_vert, color: AppTheme.spotifyOffWhite),
                        color: AppTheme.spotifyGrey,
                        onSelected: (value) => _handleSongAction(value, song, index),
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'play',
                            child: ListTile(
                              leading: Icon(Icons.play_arrow, color: AppTheme.spotifyWhite),
                              title: Text('Play', style: TextStyle(color: AppTheme.spotifyWhite)),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'add_to_playlist',
                            child: ListTile(
                              leading: Icon(Icons.playlist_add, color: AppTheme.spotifyWhite),
                              title: Text('Add to Playlist', style: TextStyle(color: AppTheme.spotifyWhite)),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'like',
                            child: ListTile(
                              leading: Icon(Icons.favorite_border, color: AppTheme.spotifyWhite),
                              title: Text('Like', style: TextStyle(color: AppTheme.spotifyWhite)),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'remove_from_history',
                            child: ListTile(
                              leading: Icon(Icons.remove_circle_outline, color: AppTheme.errorRed),
                              title: Text('Remove from History', style: TextStyle(color: AppTheme.errorRed)),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'share',
                            child: ListTile(
                              leading: Icon(Icons.share, color: AppTheme.spotifyWhite),
                              title: Text('Share', style: TextStyle(color: AppTheme.spotifyWhite)),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSubtitle(int index) {
    final song = _recentSongs[index];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          song.displayArtist,
          style: const TextStyle(
            color: AppTheme.spotifyOffWhite,
            fontSize: 14,
          ),
        ),
        Text(
          'Played ${_getTimeAgo(index)}',
          style: const TextStyle(
            color: AppTheme.spotifyOffWhite,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  String _getTimeAgo(int index) {
    // Mock time ago calculation
    final hoursAgo = index + 1;
    if (hoursAgo < 24) {
      return '${hoursAgo}h ago';
    } else {
      final daysAgo = (hoursAgo / 24).floor();
      return '${daysAgo}d ago';
    }
  }

  void _playSong(int index) {
    final audioService = Provider.of<AudioPlayerService>(context, listen: false);
    audioService.playSong(_recentSongs[index]);
  }

  void _handleSongAction(String action, Song song, int index) {
    switch (action) {
      case 'play':
        _playSong(index);
        break;
      case 'add_to_playlist':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AddToPlaylistScreen(song: song),
          ),
        );
        break;
      case 'like':
        // TODO: Add to liked songs
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Added to liked songs!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'remove_from_history':
        _removeFromHistory(song, index);
        break;
      case 'share':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sharing feature coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
    }
  }

  void _removeFromHistory(Song song, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Remove from History',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Text(
          'Remove "${song.title}" from your listening history?',
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _recentSongs.removeAt(index);
              });
              // TODO: Update user's recent history in storage
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Removed from history'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorRed,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _searchHistory() {
    // TODO: Implement search within history
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Search in history coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'clear_all':
        _showClearAllDialog();
        break;
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Export history feature coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Clear All History',
          style: TextStyle(color: AppTheme.errorRed),
        ),
        content: const Text(
          'Are you sure you want to clear your entire listening history? This action cannot be undone.',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _recentSongs.clear();
              });
              // TODO: Clear user's recent history in storage
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('History cleared'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorRed,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}
