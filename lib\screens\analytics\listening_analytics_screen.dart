import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../core/theme/app_theme.dart';
import '../../models/song.dart';
import '../../models/playlist.dart';

class ListeningAnalyticsScreen extends StatefulWidget {
  const ListeningAnalyticsScreen({super.key});

  @override
  State<ListeningAnalyticsScreen> createState() => _ListeningAnalyticsScreenState();
}

class _ListeningAnalyticsScreenState extends State<ListeningAnalyticsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'This Month';
  
  // Mock data - replace with real analytics data
  final Map<String, int> _topGenres = {
    'Pop': 45,
    'Rock': 30,
    'Hip-Hop': 25,
    'Jazz': 20,
    'Electronic': 15,
  };
  
  final List<Map<String, dynamic>> _topArtists = [
    {'name': '<PERSON>', 'plays': 156, 'hours': 8.5},
    {'name': 'The Weeknd', 'plays': 134, 'hours': 7.2},
    {'name': '<PERSON>', 'plays': 98, 'hours': 5.1},
    {'name': '<PERSON>', 'plays': 87, 'hours': 4.8},
    {'name': '<PERSON>na <PERSON>', 'plays': 76, 'hours': 4.2},
  ];
  
  final List<Map<String, dynamic>> _listeningHours = [
    {'hour': 6, 'plays': 5},
    {'hour': 8, 'plays': 15},
    {'hour': 10, 'plays': 25},
    {'hour': 12, 'plays': 35},
    {'hour': 14, 'plays': 40},
    {'hour': 16, 'plays': 45},
    {'hour': 18, 'plays': 55},
    {'hour': 20, 'plays': 60},
    {'hour': 22, 'plays': 35},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(child: _buildPeriodSelector()),
          SliverToBoxAdapter(child: _buildStatsOverview()),
          SliverToBoxAdapter(child: _buildTabBar()),
          SliverFillRemaining(child: _buildTabBarView()),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.spotifyBlack,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Your Music Stats',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.spotifyGreen.withOpacity(0.3),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    final periods = ['This Week', 'This Month', 'Last 6 Months', 'All Time'];
    
    return Container(
      height: 50,
      margin: const EdgeInsets.all(16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: periods.length,
        itemBuilder: (context, index) {
          final period = periods[index];
          final isSelected = period == _selectedPeriod;
          
          return GestureDetector(
            onTap: () => setState(() => _selectedPeriod = period),
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyGrey,
                borderRadius: BorderRadius.circular(25),
              ),
              child: Text(
                period,
                style: TextStyle(
                  color: isSelected ? AppTheme.spotifyBlack : AppTheme.spotifyWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatsOverview() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(child: _buildStatCard('Total Hours', '127.5', Icons.access_time)),
          const SizedBox(width: 12),
          Expanded(child: _buildStatCard('Songs Played', '1,234', Icons.music_note)),
          const SizedBox(width: 12),
          Expanded(child: _buildStatCard('Artists', '89', Icons.person)),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppTheme.spotifyGreen, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        tabs: const [
          Tab(text: 'Genres'),
          Tab(text: 'Artists'),
          Tab(text: 'Activity'),
          Tab(text: 'Insights'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildGenresTab(),
        _buildArtistsTab(),
        _buildActivityTab(),
        _buildInsightsTab(),
      ],
    );
  }

  Widget _buildGenresTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: _topGenres.entries.map((entry) {
                  final colors = [
                    AppTheme.spotifyGreen,
                    Colors.blue,
                    Colors.orange,
                    Colors.purple,
                    Colors.red,
                  ];
                  final index = _topGenres.keys.toList().indexOf(entry.key);
                  return PieChartSectionData(
                    value: entry.value.toDouble(),
                    title: '${entry.value}%',
                    color: colors[index % colors.length],
                    radius: 60,
                    titleStyle: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.spotifyWhite,
                    ),
                  );
                }).toList(),
                centerSpaceRadius: 40,
              ),
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: _topGenres.length,
              itemBuilder: (context, index) {
                final entry = _topGenres.entries.elementAt(index);
                final colors = [
                  AppTheme.spotifyGreen,
                  Colors.blue,
                  Colors.orange,
                  Colors.purple,
                  Colors.red,
                ];
                return ListTile(
                  leading: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: colors[index % colors.length],
                      shape: BoxShape.circle,
                    ),
                  ),
                  title: Text(
                    entry.key,
                    style: const TextStyle(color: AppTheme.spotifyWhite),
                  ),
                  trailing: Text(
                    '${entry.value}%',
                    style: const TextStyle(
                      color: AppTheme.spotifyOffWhite,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildArtistsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView.builder(
        itemCount: _topArtists.length,
        itemBuilder: (context, index) {
          final artist = _topArtists[index];
          return Card(
            color: AppTheme.spotifyGrey,
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: AppTheme.spotifyGreen,
                child: Text(
                  '${index + 1}',
                  style: const TextStyle(
                    color: AppTheme.spotifyBlack,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              title: Text(
                artist['name'],
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                '${artist['plays']} plays • ${artist['hours']} hours',
                style: const TextStyle(color: AppTheme.spotifyOffWhite),
              ),
              trailing: const Icon(
                Icons.chevron_right,
                color: AppTheme.spotifyOffWhite,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActivityTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Listening Activity by Hour',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: 70,
                barTouchData: BarTouchData(enabled: false),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          '${value.toInt()}h',
                          style: const TextStyle(
                            color: AppTheme.spotifyOffWhite,
                            fontSize: 10,
                          ),
                        );
                      },
                    ),
                  ),
                  leftTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                borderData: FlBorderData(show: false),
                barGroups: _listeningHours.map((data) {
                  return BarChartGroupData(
                    x: data['hour'],
                    barRods: [
                      BarChartRodData(
                        toY: data['plays'].toDouble(),
                        color: AppTheme.spotifyGreen,
                        width: 16,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildInsightCard(
            'Peak Listening Time',
            '8:00 PM - 10:00 PM',
            'You listen to music most during evening hours',
            Icons.schedule,
            Colors.blue,
          ),
          const SizedBox(height: 16),
          _buildInsightCard(
            'Music Discovery',
            '23% New Artists',
            'You\'re exploring new music regularly',
            Icons.explore,
            Colors.orange,
          ),
          const SizedBox(height: 16),
          _buildInsightCard(
            'Mood Pattern',
            'Energetic → Chill',
            'Your music taste shifts from upbeat to relaxed throughout the day',
            Icons.mood,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildInsightCard(String title, String value, String description, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    color: color,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
