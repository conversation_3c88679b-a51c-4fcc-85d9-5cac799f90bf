import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive/hive.dart';

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../models/playlist.dart';
import '../../models/song.dart';
import '../../services/auth_service.dart';
import '../../widgets/playlist_card.dart';
import '../playlist/playlist_view_screen.dart';
import '../playlist/create_playlist_screen.dart';
import 'liked_songs_screen.dart';
import 'recent_history_screen.dart';

class MyLibraryScreen extends StatefulWidget {
  const MyLibraryScreen({super.key});

  @override
  State<MyLibraryScreen> createState() => _MyLibraryScreenState();
}

class _MyLibraryScreenState extends State<MyLibraryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<Playlist> _playlists = [];
  List<Song> _likedSongs = [];
  List<Song> _recentlyPlayed = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadLibraryData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadLibraryData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final currentUser = authService.currentUser;

      if (currentUser != null) {
        // Load playlists
        final playlistBox = Hive.box<Playlist>(AppConstants.playlistBoxKey);
        _playlists = playlistBox.values
            .where((playlist) => playlist.createdBy == currentUser.id)
            .toList();

        // Load liked songs (placeholder)
        _likedSongs = currentUser.favoriteSongs.map((songId) {
          // In a real app, you'd fetch the actual Song objects
          return Song(
            id: songId,
            title: 'Liked Song',
            artist: 'Artist',
            album: 'Album',
            duration: 180,
          );
        }).toList();

        // Load recently played (placeholder)
        _recentlyPlayed = currentUser.recentlyPlayed.map((songId) {
          return Song(
            id: songId,
            title: 'Recently Played Song',
            artist: 'Artist',
            album: 'Album',
            duration: 180,
          );
        }).toList();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: Column(
        children: [
          // Header
          _buildHeader(),

          // Quick Access Cards
          _buildQuickAccessCards(),

          // Tab Bar
          _buildTabBar(),

          // Tab Content
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.spotifyGreen,
                      ),
                    ),
                  )
                : _buildTabContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Consumer<AuthService>(
            builder: (context, authService, child) {
              final user = authService.currentUser;
              return CircleAvatar(
                radius: 20,
                backgroundColor: AppTheme.spotifyGrey,
                child: user?.profileImageUrl != null
                    ? ClipOval(
                        child: Image.network(
                          user!.profileImageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              const Icon(
                                Icons.person,
                                color: AppTheme.spotifyOffWhite,
                              ),
                        ),
                      )
                    : const Icon(Icons.person, color: AppTheme.spotifyOffWhite),
              );
            },
          ),
          const SizedBox(width: 12),
          Text(
            'Your Library',
            style: AppTheme.darkTheme.textTheme.headlineSmall?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.search, color: AppTheme.spotifyWhite),
            onPressed: () {
              // TODO: Implement library search
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Library search coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.add, color: AppTheme.spotifyWhite),
            onPressed: _showCreateOptions,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessCards() {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Liked Songs Card
          Expanded(
            child: _buildQuickAccessCard(
              title: 'Liked Songs',
              subtitle: '${_likedSongs.length} songs',
              icon: Icons.favorite,
              color: AppTheme.spotifyGreen,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const LikedSongsScreen()),
                );
              },
            ),
          ),

          const SizedBox(width: 12),

          // Recently Played Card
          Expanded(
            child: _buildQuickAccessCard(
              title: 'Recently Played',
              subtitle: '${_recentlyPlayed.length} songs',
              icon: Icons.history,
              color: AppTheme.spotifyOffWhite,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => const RecentHistoryScreen(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.spotifyGrey,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            Text(
              subtitle,
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppTheme.spotifyBlack,
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        labelStyle: const TextStyle(fontWeight: FontWeight.w600),
        tabs: [
          Tab(text: 'Playlists (${_playlists.length})'),
          Tab(text: 'Artists (0)'), // Placeholder
          Tab(text: 'Albums (0)'), // Placeholder
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [_buildPlaylistsTab(), _buildArtistsTab(), _buildAlbumsTab()],
    );
  }

  Widget _buildPlaylistsTab() {
    if (_playlists.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.playlist_add,
              size: 64,
              color: AppTheme.spotifyOffWhite,
            ),
            const SizedBox(height: 16),
            Text(
              'No playlists yet',
              style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first playlist to get started',
              style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createPlaylist,
              icon: const Icon(Icons.add),
              label: const Text('Create Playlist'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.spotifyGreen,
                foregroundColor: AppTheme.spotifyBlack,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _playlists.length,
      itemBuilder: (context, index) {
        final playlist = _playlists[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: AppTheme.spotifyGrey,
              ),
              child: playlist.displayImage.isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        playlist.displayImage,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) =>
                            const Icon(
                              Icons.music_note,
                              color: AppTheme.spotifyOffWhite,
                            ),
                      ),
                    )
                  : const Icon(
                      Icons.music_note,
                      color: AppTheme.spotifyOffWhite,
                    ),
            ),
            title: Text(
              playlist.name,
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (playlist.description != null)
                  Text(
                    playlist.description!,
                    style: const TextStyle(
                      color: AppTheme.spotifyOffWhite,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                Text(
                  '${playlist.songs.length} songs',
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              icon: const Icon(
                Icons.more_vert,
                color: AppTheme.spotifyOffWhite,
              ),
              color: AppTheme.spotifyGrey,
              onSelected: (value) => _handlePlaylistAction(value, playlist),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'play',
                  child: ListTile(
                    leading: Icon(
                      Icons.play_arrow,
                      color: AppTheme.spotifyWhite,
                    ),
                    title: Text(
                      'Play',
                      style: TextStyle(color: AppTheme.spotifyWhite),
                    ),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit, color: AppTheme.spotifyWhite),
                    title: Text(
                      'Edit',
                      style: TextStyle(color: AppTheme.spotifyWhite),
                    ),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'share',
                  child: ListTile(
                    leading: Icon(Icons.share, color: AppTheme.spotifyWhite),
                    title: Text(
                      'Share',
                      style: TextStyle(color: AppTheme.spotifyWhite),
                    ),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: AppTheme.errorRed),
                    title: Text(
                      'Delete',
                      style: TextStyle(color: AppTheme.errorRed),
                    ),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => PlaylistViewScreen(playlist: playlist),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildArtistsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person, size: 64, color: AppTheme.spotifyOffWhite),
          SizedBox(height: 16),
          Text(
            'No followed artists yet',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 16),
          ),
          SizedBox(height: 8),
          Text(
            'Follow artists to see them here',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildAlbumsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.album, size: 64, color: AppTheme.spotifyOffWhite),
          SizedBox(height: 16),
          Text(
            'No saved albums yet',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 16),
          ),
          SizedBox(height: 8),
          Text(
            'Save albums to see them here',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 12),
          ),
        ],
      ),
    );
  }

  void _showCreateOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.spotifyGrey,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(
                Icons.playlist_add,
                color: AppTheme.spotifyWhite,
              ),
              title: const Text(
                'Create Playlist',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                _createPlaylist();
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.auto_awesome,
                color: AppTheme.spotifyGreen,
              ),
              title: const Text(
                'AI Playlist',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                _createAIPlaylist();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _createPlaylist() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const CreatePlaylistScreen()),
    ).then((_) => _loadLibraryData());
  }

  void _createAIPlaylist() {
    // TODO: Navigate to AI playlist creation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AI playlist creation coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _handlePlaylistAction(String action, Playlist playlist) {
    switch (action) {
      case 'play':
        // TODO: Play playlist
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playing "${playlist.name}"'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'edit':
        // TODO: Navigate to edit playlist
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Edit playlist coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'share':
        // TODO: Share playlist
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Share playlist coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'delete':
        _showDeletePlaylistDialog(playlist);
        break;
    }
  }

  void _showDeletePlaylistDialog(Playlist playlist) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Delete Playlist',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Text(
          'Are you sure you want to delete "${playlist.name}"? This action cannot be undone.',
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await playlist.delete();
                _loadLibraryData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Playlist deleted'),
                      backgroundColor: AppTheme.spotifyGreen,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete playlist: $e'),
                      backgroundColor: AppTheme.errorRed,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorRed),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
