import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';

import '../../core/theme/app_theme.dart';
import '../../models/song.dart';
import '../../services/audio_player_service.dart';
import '../playlist/add_to_playlist_screen.dart';

class SongDetailsScreen extends StatefulWidget {
  final Song song;

  const SongDetailsScreen({
    super.key,
    required this.song,
  });

  @override
  State<SongDetailsScreen> createState() => _SongDetailsScreenState();
}

class _SongDetailsScreenState extends State<SongDetailsScreen> {
  bool _isLiked = false;
  bool _showLyrics = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          // App Bar with song image
          _buildSliverAppBar(),
          
          // Song details
          SliverToBoxAdapter(
            child: _buildSongDetails(),
          ),
          
          // Action buttons
          SliverToBoxAdapter(
            child: _buildActionButtons(),
          ),
          
          // Lyrics or similar tracks
          SliverToBoxAdapter(
            child: _showLyrics ? _buildLyrics() : _buildSimilarTracks(),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      backgroundColor: AppTheme.spotifyBlack,
      expandedHeight: 300,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.spotifyGreen.withOpacity(0.6),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
          child: Center(
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: widget.song.albumArt != null
                    ? CachedNetworkImage(
                        imageUrl: widget.song.albumArt!,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => _buildAlbumArtPlaceholder(),
                        errorWidget: (context, url, error) => _buildAlbumArtPlaceholder(),
                      )
                    : _buildAlbumArtPlaceholder(),
              ),
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: AppTheme.spotifyWhite),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.share, color: AppTheme.spotifyWhite),
          onPressed: _shareSong,
        ),
      ],
    );
  }

  Widget _buildAlbumArtPlaceholder() {
    return Container(
      color: AppTheme.spotifyGrey,
      child: const Center(
        child: Icon(
          Icons.music_note,
          size: 80,
          color: AppTheme.spotifyOffWhite,
        ),
      ),
    );
  }

  Widget _buildSongDetails() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Song title
          Text(
            widget.song.title,
            style: AppTheme.darkTheme.textTheme.headlineMedium?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Artist
          GestureDetector(
            onTap: () {
              // TODO: Navigate to artist page
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Artist page for ${widget.song.displayArtist} coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            child: Text(
              widget.song.displayArtist,
              style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
                color: AppTheme.spotifyGreen,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Album info
          if (widget.song.album.isNotEmpty) ...[
            Row(
              children: [
                const Icon(
                  Icons.album,
                  color: AppTheme.spotifyOffWhite,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      // TODO: Navigate to album page
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Album page for ${widget.song.album} coming soon!'),
                          backgroundColor: AppTheme.spotifyGreen,
                        ),
                      );
                    },
                    child: Text(
                      widget.song.album,
                      style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
                        color: AppTheme.spotifyOffWhite,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
          
          // Duration
          Row(
            children: [
              const Icon(
                Icons.access_time,
                color: AppTheme.spotifyOffWhite,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                widget.song.formattedDuration,
                style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
                  color: AppTheme.spotifyOffWhite,
                ),
              ),
            ],
          ),
          
          // Release date
          if (widget.song.releaseDate != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  color: AppTheme.spotifyOffWhite,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${widget.song.releaseDate!.year}',
                  style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
                    color: AppTheme.spotifyOffWhite,
                  ),
                ),
              ],
            ),
          ],
          
          // Genres
          if (widget.song.genres.isNotEmpty) ...[
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.song.genres.map((genre) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.spotifyGrey,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    genre,
                    style: const TextStyle(
                      color: AppTheme.spotifyOffWhite,
                      fontSize: 12,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          // Primary actions
          Row(
            children: [
              // Play button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _playSong,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Play'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.spotifyGreen,
                    foregroundColor: AppTheme.spotifyBlack,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Like button
              IconButton(
                onPressed: _toggleLike,
                icon: Icon(
                  _isLiked ? Icons.favorite : Icons.favorite_border,
                  color: _isLiked ? AppTheme.spotifyGreen : AppTheme.spotifyOffWhite,
                ),
                iconSize: 28,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Secondary actions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Add to playlist
              _buildActionButton(
                icon: Icons.playlist_add,
                label: 'Add to Playlist',
                onPressed: _addToPlaylist,
              ),
              
              // Lyrics toggle
              _buildActionButton(
                icon: Icons.lyrics,
                label: _showLyrics ? 'Hide Lyrics' : 'Show Lyrics',
                onPressed: _toggleLyrics,
                isActive: _showLyrics,
              ),
              
              // Download
              _buildActionButton(
                icon: Icons.download,
                label: 'Download',
                onPressed: _downloadSong,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isActive = false,
  }) {
    return Column(
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(
            icon,
            color: isActive ? AppTheme.spotifyGreen : AppTheme.spotifyOffWhite,
          ),
          iconSize: 24,
        ),
        Text(
          label,
          style: TextStyle(
            color: isActive ? AppTheme.spotifyGreen : AppTheme.spotifyOffWhite,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildLyrics() {
    return Container(
      margin: const EdgeInsets.all(24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Lyrics',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Lyrics are not available for this track.\n\nThis feature will be implemented in a future update with integration to lyrics providers like Genius or Musixmatch.',
            style: TextStyle(
              color: AppTheme.spotifyOffWhite,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimilarTracks() {
    return Container(
      margin: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Similar Tracks',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.spotifyGrey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text(
                'Similar tracks feature coming soon!\n\nWe\'ll use AI to find songs similar to this one based on genre, mood, and musical characteristics.',
                style: TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  height: 1.6,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _playSong() {
    final audioService = Provider.of<AudioPlayerService>(context, listen: false);
    audioService.playSong(widget.song);
  }

  void _toggleLike() {
    setState(() {
      _isLiked = !_isLiked;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isLiked ? 'Added to liked songs' : 'Removed from liked songs'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _addToPlaylist() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddToPlaylistScreen(song: widget.song),
      ),
    );
  }

  void _toggleLyrics() {
    setState(() {
      _showLyrics = !_showLyrics;
    });
  }

  void _downloadSong() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Download feature coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _shareSong() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sharing feature coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }
}
