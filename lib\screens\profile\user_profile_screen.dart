import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../core/theme/app_theme.dart';
import '../../services/auth_service.dart';
import '../../models/user.dart';
import '../../models/playlist.dart';
import '../playlist/playlist_view_screen.dart';
import '../settings/settings_screen.dart';
import '../settings/account_settings_screen.dart';

class UserProfileScreen extends StatefulWidget {
  final String? userId; // If null, shows current user's profile

  const UserProfileScreen({
    super.key,
    this.userId,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  User? _user;
  List<Playlist> _publicPlaylists = [];
  bool _isLoading = true;
  bool _isOwnProfile = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadUserProfile();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      
      if (widget.userId == null) {
        // Show current user's profile
        _user = authService.currentUser;
        _isOwnProfile = true;
      } else {
        // Load other user's profile (placeholder - would fetch from backend)
        _user = authService.currentUser; // Placeholder
        _isOwnProfile = widget.userId == authService.currentUser?.id;
      }

      // Load public playlists (placeholder)
      _publicPlaylists = []; // Would fetch from backend

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: AppTheme.spotifyBlack,
        body: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.spotifyGreen),
          ),
        ),
      );
    }

    if (_user == null) {
      return Scaffold(
        backgroundColor: AppTheme.spotifyBlack,
        body: const Center(
          child: Text(
            'User not found',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            _buildSliverAppBar(),
          ];
        },
        body: _buildTabContent(),
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      backgroundColor: AppTheme.spotifyBlack,
      expandedHeight: 300,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.spotifyGreen.withOpacity(0.6),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
          child: _buildProfileHeader(),
        ),
      ),
      actions: [
        if (_isOwnProfile)
          IconButton(
            icon: const Icon(Icons.settings, color: AppTheme.spotifyWhite),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const SettingsScreen()),
              );
            },
          ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: AppTheme.spotifyWhite),
          color: AppTheme.spotifyGrey,
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            if (_isOwnProfile) ...[
              const PopupMenuItem(
                value: 'edit_profile',
                child: ListTile(
                  leading: Icon(Icons.edit, color: AppTheme.spotifyWhite),
                  title: Text('Edit Profile', style: TextStyle(color: AppTheme.spotifyWhite)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'account_settings',
                child: ListTile(
                  leading: Icon(Icons.account_circle, color: AppTheme.spotifyWhite),
                  title: Text('Account Settings', style: TextStyle(color: AppTheme.spotifyWhite)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ] else ...[
              const PopupMenuItem(
                value: 'follow',
                child: ListTile(
                  leading: Icon(Icons.person_add, color: AppTheme.spotifyWhite),
                  title: Text('Follow', style: TextStyle(color: AppTheme.spotifyWhite)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share, color: AppTheme.spotifyWhite),
                  title: Text('Share Profile', style: TextStyle(color: AppTheme.spotifyWhite)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        tabs: const [
          Tab(text: 'Playlists'),
          Tab(text: 'Stats'),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Profile picture
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: AppTheme.spotifyWhite, width: 3),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ClipOval(
              child: _user!.profilePicture != null
                  ? CachedNetworkImage(
                      imageUrl: _user!.profilePicture!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => _buildAvatarPlaceholder(),
                      errorWidget: (context, url, error) => _buildAvatarPlaceholder(),
                    )
                  : _buildAvatarPlaceholder(),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Display name
          Text(
            _user!.displayName,
            style: AppTheme.darkTheme.textTheme.headlineMedium?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // Email (only for own profile)
          if (_isOwnProfile)
            Text(
              _user!.email,
              style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
              textAlign: TextAlign.center,
            ),
          
          const SizedBox(height: 16),
          
          // Stats row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('Playlists', '${_publicPlaylists.length}'),
              _buildStatItem('Followers', '0'), // Placeholder
              _buildStatItem('Following', '0'), // Placeholder
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarPlaceholder() {
    return Container(
      color: AppTheme.spotifyGrey,
      child: const Center(
        child: Icon(
          Icons.person,
          size: 48,
          color: AppTheme.spotifyOffWhite,
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTheme.darkTheme.textTheme.bodySmall?.copyWith(
            color: AppTheme.spotifyOffWhite,
          ),
        ),
      ],
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildPlaylistsTab(),
        _buildStatsTab(),
      ],
    );
  }

  Widget _buildPlaylistsTab() {
    if (_publicPlaylists.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.playlist_play,
              size: 64,
              color: AppTheme.spotifyOffWhite,
            ),
            const SizedBox(height: 16),
            Text(
              _isOwnProfile ? 'No public playlists yet' : 'No public playlists',
              style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
            ),
            if (_isOwnProfile) ...[
              const SizedBox(height: 8),
              Text(
                'Create a playlist and make it public to share with others',
                style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.spotifyOffWhite,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _publicPlaylists.length,
      itemBuilder: (context, index) {
        final playlist = _publicPlaylists[index];
        return ListTile(
          leading: Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: AppTheme.spotifyGrey,
            ),
            child: const Icon(
              Icons.music_note,
              color: AppTheme.spotifyOffWhite,
            ),
          ),
          title: Text(
            playlist.name,
            style: const TextStyle(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.w500,
            ),
          ),
          subtitle: Text(
            '${playlist.songs.length} songs',
            style: const TextStyle(
              color: AppTheme.spotifyOffWhite,
            ),
          ),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => PlaylistViewScreen(playlist: playlist),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildStatsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Profile Statistics',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 24),
          
          _buildStatCard('Member Since', _formatDate(_user!.createdAt)),
          _buildStatCard('Last Active', _formatDate(_user!.lastActiveAt)),
          _buildStatCard('Total Playlists', '0'), // Placeholder
          _buildStatCard('Total Songs Liked', '0'), // Placeholder
          _buildStatCard('Hours Listened', '0'), // Placeholder
          
          const SizedBox(height: 24),
          
          if (_isOwnProfile) ...[
            Text(
              'Privacy Settings',
              style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: const Text(
                'Public Profile',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              subtitle: const Text(
                'Allow others to see your profile',
                style: TextStyle(color: AppTheme.spotifyOffWhite),
              ),
              value: true, // Placeholder
              onChanged: (value) {
                // TODO: Update privacy setting
              },
              activeColor: AppTheme.spotifyGreen,
            ),
            
            SwitchListTile(
              title: const Text(
                'Show Listening Activity',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              subtitle: const Text(
                'Let others see what you\'re listening to',
                style: TextStyle(color: AppTheme.spotifyOffWhite),
              ),
              value: false, // Placeholder
              onChanged: (value) {
                // TODO: Update privacy setting
              },
              activeColor: AppTheme.spotifyGreen,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 16,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} year${(difference.inDays / 365).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} month${(difference.inDays / 30).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit_profile':
        // TODO: Navigate to edit profile screen
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Edit profile feature coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'account_settings':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AccountSettingsScreen()),
        );
        break;
      case 'follow':
        // TODO: Implement follow functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Follow feature coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'share':
        // TODO: Implement share profile
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Share profile feature coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
    }
  }
}
